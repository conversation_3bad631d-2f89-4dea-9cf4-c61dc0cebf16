#!/usr/bin/env python3
"""
Test the improved auto-detection with real data from Testing-Scores.txt
"""

def test_with_real_data():
    """Test with actual data from the file"""
    
    print("🎾 TESTING WITH REAL DATA")
    print("=" * 50)
    
    # Test SET 1 data (HUR should be detected as starting server)
    print("\n📋 TEST: SET 1 (<PERSON>UR started serving)")
    
    # Simulate the tennis scores from SET 1 first game
    tennis_scores = ['15', '0', '30', '0', '40', '0', '40', '15', '40', '30']
    game_winner = 'HUR'  # From the file: "<PERSON>" won first game
    
    point_sequence = reconstruct_points_with_winner(tennis_scores, 'HUR', 'DJO', game_winner)
    print(f"🔍 Final point sequence: {point_sequence}")
    
    # Now test both hypotheses
    print("\n🧪 HYPOTHESIS TESTING:")
    
    # Hypothesis 1: <PERSON><PERSON> was serving
    print("Hypothesis 1: HUR serving")
    sim1 = simulate_game(point_sequence, 'HUR', 'DJO', 'HUR')
    print(f"   Result: {sim1}")
    
    # Hypothesis 2: <PERSON><PERSON> was serving  
    print("Hypothesis 2: <PERSON><PERSON> serving")
    sim2 = simulate_game(point_sequence, 'HUR', 'DJO', 'DJO')
    print(f"   Result: {sim2}")
    
    # Determine correct hypothesis
    if sim1['winner'] == game_winner:
        print(f"✅ CONCLUSION: HUR started serving (matches actual)")
    elif sim2['winner'] == game_winner:
        print(f"✅ CONCLUSION: DJO started serving (matches actual)")
    else:
        print(f"❌ ERROR: Neither hypothesis matches")
        
    print(f"\n🎯 Expected: HUR started serving")
    print(f"🎯 Detected: {'HUR' if sim1['winner'] == game_winner else 'DJO' if sim2['winner'] == game_winner else 'UNKNOWN'}")

def reconstruct_points_with_winner(tennis_scores, player1_code, player2_code, game_winner):
    """Reconstruct points and add final point if needed"""
    
    point_sequence = []
    
    # Process scores in pairs
    for i in range(0, len(tennis_scores) - 1, 2):
        if i + 1 >= len(tennis_scores):
            break
            
        current_p1 = tennis_scores[i]
        current_p2 = tennis_scores[i + 1]
        
        if i == 0:
            # First score pair
            if current_p1 == '15' and current_p2 == '0':
                point_sequence.append(player1_code)
                print(f"   Point 1: {player1_code} (15-0)")
            elif current_p1 == '0' and current_p2 == '15':
                point_sequence.append(player2_code)
                print(f"   Point 1: {player2_code} (0-15)")
        else:
            # Compare with previous score
            prev_p1 = tennis_scores[i - 2]
            prev_p2 = tennis_scores[i - 1]
            
            point_winner = determine_point_winner(prev_p1, prev_p2, current_p1, current_p2, player1_code, player2_code)
            if point_winner:
                point_sequence.append(point_winner)
                print(f"   Point {len(point_sequence)}: {point_winner} ({current_p1}-{current_p2})")
    
    # Check if game is complete
    game_complete = check_game_complete(point_sequence)
    if not game_complete and game_winner:
        print(f"🔍 Game incomplete - adding final point for winner {game_winner}")
        point_sequence.append(game_winner)
        print(f"   Final point: {game_winner} (game winner)")
    
    return point_sequence

def check_game_complete(point_sequence):
    """Check if the point sequence represents a complete game"""
    if len(point_sequence) < 4:
        return False
        
    # Count points for each player (assuming first player in sequence)
    players = list(set(point_sequence))
    if len(players) != 2:
        return False
        
    p1_points = point_sequence.count(players[0])
    p2_points = point_sequence.count(players[1])
    
    # Check win conditions
    if p1_points >= 4 and p1_points >= p2_points + 2:
        return True
    elif p2_points >= 4 and p2_points >= p1_points + 2:
        return True
        
    return False

def determine_point_winner(prev_p1, prev_p2, curr_p1, curr_p2, player1_code, player2_code):
    """Determine who won a point based on score progression"""
    
    score_map = {'0': 0, '15': 1, '30': 2, '40': 3, 'AD': 4, 'BP': 3}
    
    try:
        prev_p1_val = score_map.get(prev_p1, 0)
        prev_p2_val = score_map.get(prev_p2, 0)
        curr_p1_val = score_map.get(curr_p1, 0)
        curr_p2_val = score_map.get(curr_p2, 0)
        
        # Check if player 1's score increased
        if curr_p1_val > prev_p1_val:
            return player1_code
        # Check if player 2's score increased  
        elif curr_p2_val > prev_p2_val:
            return player2_code
        # Handle special cases
        elif curr_p1 == 'AD' and prev_p1 != 'AD':
            return player1_code
        elif curr_p2 == 'AD' and prev_p2 != 'AD':
            return player2_code
            
    except Exception as e:
        print(f"Error: {e}")
        
    return None

def simulate_game(point_sequence, player1_code, player2_code, server_code):
    """Simulate a tennis game"""
    server_score = 0
    receiver_score = 0
    receiver_code = player2_code if server_code == player1_code else player1_code
    
    for point_winner in point_sequence:
        if point_winner == server_code:
            server_score += 1
        elif point_winner == receiver_code:
            receiver_score += 1
        
        # Check for game end
        if server_score >= 4 and server_score >= receiver_score + 2:
            return {'winner': server_code, 'final_score': f"{server_score}-{receiver_score}"}
        elif receiver_score >= 4 and receiver_score >= server_score + 2:
            return {'winner': receiver_code, 'final_score': f"{server_score}-{receiver_score}"}
    
    return {'winner': None, 'final_score': f"{server_score}-{receiver_score}"}

if __name__ == "__main__":
    test_with_real_data()
