#!/usr/bin/env python3
"""
Test the point reconstruction logic with real data from Testing-Scores.txt
"""

def test_point_reconstruction():
    """Test reconstructing points from tennis scores"""
    
    print("🎾 TESTING POINT RECONSTRUCTION")
    print("=" * 50)
    
    # Test case from SET 1 first game: 15-0, 30-0, 40-0, 40-15, 40-30
    # This should show HUR winning first 3 points, then DJO winning 2 points
    print("\n📋 TEST: SET 1 First Game Score Progression")
    tennis_scores = ['15', '0', '30', '0', '40', '0', '40', '15', '40', '30']
    
    point_sequence = reconstruct_points_from_scores(tennis_scores, 'HUR', 'DJO')
    print(f"🔍 Reconstructed sequence: {point_sequence}")
    
    # Expected: HUR, HUR, HUR, <PERSON><PERSON>, <PERSON><PERSON> (HUR wins 3-2 but game incomplete)
    expected_length = 5
    if len(point_sequence) == expected_length:
        print("✅ PASS: Correct number of points reconstructed")
    else:
        print(f"❌ FAIL: Expected {expected_length} points, got {len(point_sequence)}")
    
    # Test case from SET 2 first game (more complex with AD)
    print("\n📋 TEST: SET 2 First Game (with Advantage)")
    tennis_scores_set2 = ['0', '15', '15', '15', '30', '15', '40', '15', '40', '30', '40', '40', 'AD', '40', '40', '40', 'AD', '40', '40', '40', '40', 'BP', 'AD', '40', '40', 'AD', '40']
    
    point_sequence_set2 = reconstruct_points_from_scores(tennis_scores_set2, 'HUR', 'DJO')
    print(f"🔍 Reconstructed sequence: {point_sequence_set2}")
    print(f"🔍 Total points: {len(point_sequence_set2)}")

def reconstruct_points_from_scores(tennis_scores, player1_code, player2_code):
    """Simplified version of the reconstruction logic"""
    
    if len(tennis_scores) < 2:
        print(f"⚠️ Not enough scores: {tennis_scores}")
        return []
        
    point_sequence = []
    
    # Process scores in pairs
    for i in range(0, len(tennis_scores) - 1, 2):
        if i + 1 >= len(tennis_scores):
            break
            
        current_p1 = tennis_scores[i]
        current_p2 = tennis_scores[i + 1]
        
        if i == 0:
            # First score pair
            if current_p1 == '15' and current_p2 == '0':
                point_sequence.append(player1_code)
                print(f"   Point 1: {player1_code} (15-0)")
            elif current_p1 == '0' and current_p2 == '15':
                point_sequence.append(player2_code)
                print(f"   Point 1: {player2_code} (0-15)")
        else:
            # Compare with previous score
            prev_p1 = tennis_scores[i - 2]
            prev_p2 = tennis_scores[i - 1]
            
            point_winner = determine_point_winner(prev_p1, prev_p2, current_p1, current_p2, player1_code, player2_code)
            if point_winner:
                point_sequence.append(point_winner)
                print(f"   Point {len(point_sequence)}: {point_winner} ({current_p1}-{current_p2})")
    
    return point_sequence

def determine_point_winner(prev_p1, prev_p2, curr_p1, curr_p2, player1_code, player2_code):
    """Determine who won a point based on score progression"""
    
    score_map = {'0': 0, '15': 1, '30': 2, '40': 3, 'AD': 4, 'BP': 3}
    
    try:
        prev_p1_val = score_map.get(prev_p1, 0)
        prev_p2_val = score_map.get(prev_p2, 0)
        curr_p1_val = score_map.get(curr_p1, 0)
        curr_p2_val = score_map.get(curr_p2, 0)
        
        # Check if player 1's score increased
        if curr_p1_val > prev_p1_val:
            return player1_code
        # Check if player 2's score increased  
        elif curr_p2_val > prev_p2_val:
            return player2_code
        # Handle special cases
        elif curr_p1 == 'AD' and prev_p1 != 'AD':
            return player1_code
        elif curr_p2 == 'AD' and prev_p2 != 'AD':
            return player2_code
            
    except Exception as e:
        print(f"Error: {e}")
        
    return None

if __name__ == "__main__":
    test_point_reconstruction()
